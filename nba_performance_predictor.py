#!/usr/bin/env python3
"""
NBA Player Performance Prediction Model

This script creates a machine learning model to predict NBA player points scored
based on various game and historical performance features.

Author: AI Assistant
Date: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

class NBAPerformancePredictor:
    """
    A comprehensive NBA player performance prediction system.
    """
    
    def __init__(self, data_path='nba_player_full_stats.csv'):
        """Initialize the predictor with data path."""
        self.data_path = data_path
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.best_model = None
        self.feature_names = None
        self.label_encoders = {}
        self.scaler = StandardScaler()
        
    def load_and_explore_data(self):
        """Load data and perform initial exploration."""
        print("=" * 60)
        print("NBA PLAYER PERFORMANCE PREDICTION SYSTEM")
        print("=" * 60)
        
        # Load data
        self.data = pd.read_csv(self.data_path)
        print(f"\n📊 Dataset loaded successfully!")
        print(f"Shape: {self.data.shape}")
        print(f"Columns: {list(self.data.columns)}")
        
        # Basic info
        print("\n📋 Dataset Info:")
        print(self.data.info())
        
        # Statistical summary
        print("\n📈 Statistical Summary:")
        print(self.data.describe())
        
        # Check for missing values
        print("\n🔍 Missing Values:")
        missing_values = self.data.isnull().sum()
        print(missing_values[missing_values > 0] if missing_values.sum() > 0 else "No missing values found!")
        
        # Check for duplicates
        duplicates = self.data.duplicated().sum()
        print(f"\n🔄 Duplicate rows: {duplicates}")
        
        return self.data
    
    def visualize_data(self):
        """Create comprehensive data visualizations."""
        print("\n🎨 Creating data visualizations...")
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create figure with subplots
        fig = plt.figure(figsize=(20, 15))
        
        # 1. Target variable distribution
        plt.subplot(3, 4, 1)
        plt.hist(self.data['Points_Scored'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('Distribution of Points Scored', fontsize=12, fontweight='bold')
        plt.xlabel('Points Scored')
        plt.ylabel('Frequency')
        
        # 2. Correlation heatmap
        plt.subplot(3, 4, 2)
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        correlation_matrix = self.data[numeric_cols].corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
                   square=True, fmt='.2f', cbar_kws={'shrink': 0.8})
        plt.title('Feature Correlation Matrix', fontsize=12, fontweight='bold')
        
        # 3. Points vs Minutes Played
        plt.subplot(3, 4, 3)
        plt.scatter(self.data['Minutes_Played'], self.data['Points_Scored'], alpha=0.6, color='green')
        plt.xlabel('Minutes Played')
        plt.ylabel('Points Scored')
        plt.title('Points vs Minutes Played', fontsize=12, fontweight='bold')
        
        # 4. Points vs Last 5 Game Average
        plt.subplot(3, 4, 4)
        plt.scatter(self.data['Last5_AvgPoints'], self.data['Points_Scored'], alpha=0.6, color='orange')
        plt.xlabel('Last 5 Games Avg Points')
        plt.ylabel('Points Scored')
        plt.title('Points vs Recent Performance', fontsize=12, fontweight='bold')
        
        # 5. Home vs Away performance
        plt.subplot(3, 4, 5)
        home_away_stats = self.data.groupby('Home_Game')['Points_Scored'].agg(['mean', 'std']).reset_index()
        home_away_stats['Home_Game'] = home_away_stats['Home_Game'].map({0: 'Away', 1: 'Home'})
        plt.bar(home_away_stats['Home_Game'], home_away_stats['mean'], 
                yerr=home_away_stats['std'], capsize=5, alpha=0.7, color=['red', 'blue'])
        plt.title('Home vs Away Performance', fontsize=12, fontweight='bold')
        plt.ylabel('Average Points Scored')
        
        # 6. Top performers
        plt.subplot(3, 4, 6)
        top_performers = self.data.groupby('Player')['Points_Scored'].mean().sort_values(ascending=False).head(10)
        plt.barh(range(len(top_performers)), top_performers.values, color='purple', alpha=0.7)
        plt.yticks(range(len(top_performers)), top_performers.index, fontsize=8)
        plt.xlabel('Average Points Scored')
        plt.title('Top 10 Performers (Avg Points)', fontsize=12, fontweight='bold')
        
        # 7. Rebounds vs Points
        plt.subplot(3, 4, 7)
        plt.scatter(self.data['Rebounds'], self.data['Points_Scored'], alpha=0.6, color='brown')
        plt.xlabel('Rebounds')
        plt.ylabel('Points Scored')
        plt.title('Rebounds vs Points', fontsize=12, fontweight='bold')
        
        # 8. Assists vs Points
        plt.subplot(3, 4, 8)
        plt.scatter(self.data['Assists'], self.data['Points_Scored'], alpha=0.6, color='pink')
        plt.xlabel('Assists')
        plt.ylabel('Points Scored')
        plt.title('Assists vs Points', fontsize=12, fontweight='bold')
        
        # 9. Feature importance preview (using correlation with target)
        plt.subplot(3, 4, 9)
        feature_corr = correlation_matrix['Points_Scored'].drop('Points_Scored').sort_values(key=abs, ascending=False)
        plt.barh(range(len(feature_corr)), feature_corr.values, color='teal', alpha=0.7)
        plt.yticks(range(len(feature_corr)), feature_corr.index, fontsize=8)
        plt.xlabel('Correlation with Points Scored')
        plt.title('Feature Correlation with Target', fontsize=12, fontweight='bold')
        
        # 10. Opponent difficulty analysis
        plt.subplot(3, 4, 10)
        opponent_stats = self.data.groupby('Opponent')['Points_Scored'].mean().sort_values(ascending=False)
        plt.bar(range(len(opponent_stats)), opponent_stats.values, alpha=0.7, color='gold')
        plt.xticks(range(len(opponent_stats)), opponent_stats.index, rotation=45, fontsize=8)
        plt.ylabel('Average Points Allowed')
        plt.title('Points Scored vs Opponents', fontsize=12, fontweight='bold')
        
        # 11. Steals and Blocks distribution
        plt.subplot(3, 4, 11)
        plt.scatter(self.data['Steals'], self.data['Blocks'], c=self.data['Points_Scored'], 
                   cmap='viridis', alpha=0.6)
        plt.colorbar(label='Points Scored')
        plt.xlabel('Steals')
        plt.ylabel('Blocks')
        plt.title('Defensive Stats vs Points', fontsize=12, fontweight='bold')
        
        # 12. Performance consistency
        plt.subplot(3, 4, 12)
        player_consistency = self.data.groupby('Player')['Points_Scored'].std().sort_values(ascending=True).head(10)
        plt.barh(range(len(player_consistency)), player_consistency.values, color='lightcoral', alpha=0.7)
        plt.yticks(range(len(player_consistency)), player_consistency.index, fontsize=8)
        plt.xlabel('Points Standard Deviation')
        plt.title('Most Consistent Players', fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('nba_data_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ Visualizations saved as 'nba_data_analysis.png'")

    def preprocess_data(self):
        """Clean and preprocess the data for machine learning."""
        print("\n🔧 Preprocessing data...")

        # Create a copy for preprocessing
        df = self.data.copy()

        # Handle missing values (if any)
        if df.isnull().sum().sum() > 0:
            print("Handling missing values...")
            # Fill numerical columns with median
            numerical_cols = df.select_dtypes(include=[np.number]).columns
            for col in numerical_cols:
                if df[col].isnull().sum() > 0:
                    df[col].fillna(df[col].median(), inplace=True)

            # Fill categorical columns with mode
            categorical_cols = df.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                if df[col].isnull().sum() > 0:
                    df[col].fillna(df[col].mode()[0], inplace=True)

        # Remove duplicates
        initial_shape = df.shape[0]
        df = df.drop_duplicates()
        if df.shape[0] < initial_shape:
            print(f"Removed {initial_shape - df.shape[0]} duplicate rows")

        # Feature Engineering
        print("Creating new features...")

        # 1. Player efficiency metrics
        df['Points_Per_Minute'] = df['Points_Scored'] / df['Minutes_Played']
        df['Total_Stats'] = df['Rebounds'] + df['Assists'] + df['Steals'] + df['Blocks']
        df['Offensive_Rating'] = (df['Points_Scored'] + df['Assists']) / df['Minutes_Played']
        df['Defensive_Rating'] = (df['Steals'] + df['Blocks']) / df['Minutes_Played']

        # 2. Performance vs expectation
        df['Points_vs_Recent'] = df['Points_Scored'] - df['Last5_AvgPoints']

        # 3. Player position estimation (based on stats pattern)
        df['Likely_Guard'] = ((df['Assists'] > df['Assists'].median()) &
                             (df['Rebounds'] < df['Rebounds'].median())).astype(int)
        df['Likely_Center'] = ((df['Rebounds'] > df['Rebounds'].quantile(0.75)) &
                              (df['Blocks'] > df['Blocks'].median())).astype(int)

        # Encode categorical variables
        print("Encoding categorical variables...")

        # Label encode Player and Opponent
        for col in ['Player', 'Opponent']:
            le = LabelEncoder()
            df[f'{col}_encoded'] = le.fit_transform(df[col])
            self.label_encoders[col] = le

        # Prepare features and target
        feature_cols = [
            'Minutes_Played', 'Last5_AvgPoints', 'Home_Game', 'Rebounds', 'Assists',
            'Steals', 'Blocks', 'Player_encoded', 'Opponent_encoded',
            'Points_Per_Minute', 'Total_Stats', 'Offensive_Rating', 'Defensive_Rating',
            'Points_vs_Recent', 'Likely_Guard', 'Likely_Center'
        ]

        X = df[feature_cols]
        y = df['Points_Scored']

        # Remove Points_Per_Minute as it contains target information
        X = X.drop('Points_Per_Minute', axis=1)
        feature_cols.remove('Points_Per_Minute')

        self.feature_names = feature_cols

        # Split the data
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=None
        )

        # Scale numerical features
        numerical_features = [col for col in feature_cols if col not in ['Player_encoded', 'Opponent_encoded', 'Home_Game', 'Likely_Guard', 'Likely_Center']]

        # Fit scaler on training data only
        self.X_train_scaled = self.X_train.copy()
        self.X_test_scaled = self.X_test.copy()

        self.X_train_scaled[numerical_features] = self.scaler.fit_transform(self.X_train[numerical_features])
        self.X_test_scaled[numerical_features] = self.scaler.transform(self.X_test[numerical_features])

        print(f"✅ Data preprocessing complete!")
        print(f"Training set shape: {self.X_train.shape}")
        print(f"Test set shape: {self.X_test.shape}")
        print(f"Features: {self.feature_names}")

        return self.X_train, self.X_test, self.y_train, self.y_test

    def train_models(self):
        """Train multiple machine learning models and compare performance."""
        print("\n🤖 Training machine learning models...")

        # Define models to train
        models_to_train = {
            'Linear Regression': LinearRegression(),
            'Ridge Regression': Ridge(alpha=1.0),
            'Lasso Regression': Lasso(alpha=1.0),
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42)
        }

        # Train and evaluate each model
        model_scores = {}

        for name, model in models_to_train.items():
            print(f"\nTraining {name}...")

            # Use scaled data for linear models, original for tree-based models
            if 'Regression' in name:
                X_train_use = self.X_train_scaled
                X_test_use = self.X_test_scaled
            else:
                X_train_use = self.X_train
                X_test_use = self.X_test

            # Train the model
            model.fit(X_train_use, self.y_train)

            # Make predictions
            y_pred_train = model.predict(X_train_use)
            y_pred_test = model.predict(X_test_use)

            # Calculate metrics
            train_r2 = r2_score(self.y_train, y_pred_train)
            test_r2 = r2_score(self.y_test, y_pred_test)
            train_rmse = np.sqrt(mean_squared_error(self.y_train, y_pred_train))
            test_rmse = np.sqrt(mean_squared_error(self.y_test, y_pred_test))
            test_mae = mean_absolute_error(self.y_test, y_pred_test)

            # Cross-validation score
            cv_scores = cross_val_score(model, X_train_use, self.y_train, cv=5, scoring='r2')

            # Store results
            model_scores[name] = {
                'model': model,
                'train_r2': train_r2,
                'test_r2': test_r2,
                'train_rmse': train_rmse,
                'test_rmse': test_rmse,
                'test_mae': test_mae,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'predictions': y_pred_test,
                'use_scaled': 'Regression' in name
            }

            print(f"  Train R²: {train_r2:.4f}")
            print(f"  Test R²: {test_r2:.4f}")
            print(f"  Test RMSE: {test_rmse:.4f}")
            print(f"  Test MAE: {test_mae:.4f}")
            print(f"  CV R² (mean ± std): {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

        self.models = model_scores

        # Select best model based on test R²
        best_model_name = max(model_scores.keys(), key=lambda x: model_scores[x]['test_r2'])
        self.best_model = model_scores[best_model_name]
        self.best_model['name'] = best_model_name

        print(f"\n🏆 Best model: {best_model_name} (Test R²: {self.best_model['test_r2']:.4f})")

        return model_scores

    def hyperparameter_tuning(self):
        """Perform hyperparameter tuning on the best performing model."""
        print(f"\n⚙️ Performing hyperparameter tuning on {self.best_model['name']}...")

        if 'Random Forest' in self.best_model['name']:
            # Random Forest hyperparameter tuning
            param_grid = {
                'n_estimators': [50, 100, 200],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            }

            base_model = RandomForestRegressor(random_state=42)
            X_use = self.X_train

        elif 'Gradient Boosting' in self.best_model['name']:
            # Gradient Boosting hyperparameter tuning
            param_grid = {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7],
                'min_samples_split': [2, 5, 10]
            }

            base_model = GradientBoostingRegressor(random_state=42)
            X_use = self.X_train

        else:
            # For regression models
            if 'Ridge' in self.best_model['name']:
                param_grid = {'alpha': [0.1, 1.0, 10.0, 100.0]}
                base_model = Ridge()
            elif 'Lasso' in self.best_model['name']:
                param_grid = {'alpha': [0.1, 1.0, 10.0, 100.0]}
                base_model = Lasso()
            else:
                print("No hyperparameter tuning needed for Linear Regression")
                return self.best_model['model']

            X_use = self.X_train_scaled

        # Perform grid search
        grid_search = GridSearchCV(
            base_model, param_grid, cv=5, scoring='r2', n_jobs=-1, verbose=1
        )

        grid_search.fit(X_use, self.y_train)

        # Update best model
        tuned_model = grid_search.best_estimator_

        print(f"Best parameters: {grid_search.best_params_}")
        print(f"Best CV score: {grid_search.best_score_:.4f}")

        # Evaluate tuned model
        X_test_use = self.X_test_scaled if self.best_model['use_scaled'] else self.X_test
        y_pred_tuned = tuned_model.predict(X_test_use)
        tuned_r2 = r2_score(self.y_test, y_pred_tuned)
        tuned_rmse = np.sqrt(mean_squared_error(self.y_test, y_pred_tuned))

        print(f"Tuned model Test R²: {tuned_r2:.4f}")
        print(f"Tuned model Test RMSE: {tuned_rmse:.4f}")

        # Update best model if tuned version is better
        if tuned_r2 > self.best_model['test_r2']:
            self.best_model['model'] = tuned_model
            self.best_model['test_r2'] = tuned_r2
            self.best_model['test_rmse'] = tuned_rmse
            self.best_model['predictions'] = y_pred_tuned
            print("✅ Tuned model is better - updated best model")
        else:
            print("Original model performs better - keeping original")

        return tuned_model

    def evaluate_model(self):
        """Comprehensive model evaluation with visualizations."""
        print("\n📊 Evaluating model performance...")

        # Get predictions from best model
        X_test_use = self.X_test_scaled if self.best_model['use_scaled'] else self.X_test
        y_pred = self.best_model['predictions']

        # Create evaluation plots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 1. Actual vs Predicted
        axes[0, 0].scatter(self.y_test, y_pred, alpha=0.6, color='blue')
        axes[0, 0].plot([self.y_test.min(), self.y_test.max()],
                       [self.y_test.min(), self.y_test.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('Actual Points')
        axes[0, 0].set_ylabel('Predicted Points')
        axes[0, 0].set_title(f'Actual vs Predicted\nR² = {self.best_model["test_r2"]:.4f}')

        # 2. Residuals plot
        residuals = self.y_test - y_pred
        axes[0, 1].scatter(y_pred, residuals, alpha=0.6, color='green')
        axes[0, 1].axhline(y=0, color='r', linestyle='--')
        axes[0, 1].set_xlabel('Predicted Points')
        axes[0, 1].set_ylabel('Residuals')
        axes[0, 1].set_title('Residuals Plot')

        # 3. Residuals distribution
        axes[0, 2].hist(residuals, bins=20, alpha=0.7, color='orange', edgecolor='black')
        axes[0, 2].set_xlabel('Residuals')
        axes[0, 2].set_ylabel('Frequency')
        axes[0, 2].set_title('Residuals Distribution')

        # 4. Feature importance (for tree-based models)
        if hasattr(self.best_model['model'], 'feature_importances_'):
            feature_importance = pd.DataFrame({
                'feature': self.feature_names,
                'importance': self.best_model['model'].feature_importances_
            }).sort_values('importance', ascending=False)

            axes[1, 0].barh(range(len(feature_importance)), feature_importance['importance'], color='purple', alpha=0.7)
            axes[1, 0].set_yticks(range(len(feature_importance)))
            axes[1, 0].set_yticklabels(feature_importance['feature'], fontsize=8)
            axes[1, 0].set_xlabel('Feature Importance')
            axes[1, 0].set_title('Feature Importance')
        else:
            axes[1, 0].text(0.5, 0.5, 'Feature importance\nnot available for\nlinear models',
                           ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('Feature Importance')

        # 5. Model comparison
        model_names = list(self.models.keys())
        test_r2_scores = [self.models[name]['test_r2'] for name in model_names]

        axes[1, 1].bar(model_names, test_r2_scores, alpha=0.7, color='teal')
        axes[1, 1].set_ylabel('Test R² Score')
        axes[1, 1].set_title('Model Comparison')
        axes[1, 1].tick_params(axis='x', rotation=45)

        # 6. Prediction error distribution
        abs_errors = np.abs(residuals)
        axes[1, 2].boxplot([abs_errors], labels=['Absolute Errors'])
        axes[1, 2].set_ylabel('Absolute Error')
        axes[1, 2].set_title(f'Prediction Errors\nMAE = {self.best_model.get("test_mae", np.mean(abs_errors)):.2f}')

        plt.tight_layout()
        plt.savefig('model_evaluation.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Print detailed evaluation metrics
        print(f"\n🎯 Model Performance Summary:")
        print(f"Model: {self.best_model['name']}")
        print(f"Test R² Score: {self.best_model['test_r2']:.4f}")
        print(f"Test RMSE: {self.best_model['test_rmse']:.4f}")
        print(f"Test MAE: {self.best_model.get('test_mae', np.mean(abs_errors)):.4f}")
        print(f"Mean Absolute Percentage Error: {np.mean(np.abs(residuals / self.y_test)) * 100:.2f}%")

        # Performance by prediction range
        print(f"\n📈 Performance by Prediction Range:")
        ranges = [(0, 20), (20, 30), (30, 40), (40, 50)]
        for low, high in ranges:
            mask = (self.y_test >= low) & (self.y_test < high)
            if mask.sum() > 0:
                range_mae = np.mean(np.abs(residuals[mask]))
                print(f"  {low}-{high} points: MAE = {range_mae:.2f} ({mask.sum()} samples)")

        print("✅ Model evaluation complete! Plots saved as 'model_evaluation.png'")

    def predict_new_data(self, new_data):
        """Make predictions on new data."""
        print("\n🔮 Making predictions on new data...")

        # Ensure new_data is a DataFrame
        if isinstance(new_data, dict):
            new_data = pd.DataFrame([new_data])

        # Preprocess new data similar to training data
        processed_data = new_data.copy()

        # Add engineered features
        processed_data['Total_Stats'] = (processed_data['Rebounds'] + processed_data['Assists'] +
                                       processed_data['Steals'] + processed_data['Blocks'])
        processed_data['Offensive_Rating'] = ((processed_data['Points_Scored'] + processed_data['Assists']) /
                                            processed_data['Minutes_Played'] if 'Points_Scored' in processed_data.columns
                                            else processed_data['Assists'] / processed_data['Minutes_Played'])
        processed_data['Defensive_Rating'] = ((processed_data['Steals'] + processed_data['Blocks']) /
                                            processed_data['Minutes_Played'])
        processed_data['Points_vs_Recent'] = (processed_data.get('Points_Scored', processed_data['Last5_AvgPoints']) -
                                            processed_data['Last5_AvgPoints'])
        processed_data['Likely_Guard'] = ((processed_data['Assists'] > self.data['Assists'].median()) &
                                        (processed_data['Rebounds'] < self.data['Rebounds'].median())).astype(int)
        processed_data['Likely_Center'] = ((processed_data['Rebounds'] > self.data['Rebounds'].quantile(0.75)) &
                                         (processed_data['Blocks'] > self.data['Blocks'].median())).astype(int)

        # Encode categorical variables
        for col in ['Player', 'Opponent']:
            if col in processed_data.columns:
                # Handle unseen categories
                try:
                    processed_data[f'{col}_encoded'] = self.label_encoders[col].transform(processed_data[col])
                except ValueError:
                    # Assign a default value for unseen categories
                    processed_data[f'{col}_encoded'] = 0
                    print(f"Warning: Unknown {col} found, using default encoding")

        # Select features (excluding target if present)
        feature_cols = [col for col in self.feature_names if col in processed_data.columns]
        X_new = processed_data[feature_cols]

        # Scale if needed
        if self.best_model['use_scaled']:
            numerical_features = [col for col in feature_cols if col not in ['Player_encoded', 'Opponent_encoded', 'Home_Game', 'Likely_Guard', 'Likely_Center']]
            X_new_scaled = X_new.copy()
            X_new_scaled[numerical_features] = self.scaler.transform(X_new[numerical_features])
            X_use = X_new_scaled
        else:
            X_use = X_new

        # Make predictions
        predictions = self.best_model['model'].predict(X_use)

        # Create results DataFrame
        results = processed_data.copy()
        results['Predicted_Points'] = predictions

        print(f"✅ Predictions completed for {len(predictions)} samples")

        return results[['Player', 'Opponent', 'Predicted_Points'] +
                      [col for col in ['Minutes_Played', 'Last5_AvgPoints', 'Home_Game'] if col in results.columns]]

    def save_model(self, filename='nba_predictor_model.pkl'):
        """Save the trained model and preprocessors."""
        import pickle

        model_data = {
            'best_model': self.best_model,
            'label_encoders': self.label_encoders,
            'scaler': self.scaler,
            'feature_names': self.feature_names,
            'data_stats': {
                'assists_median': self.data['Assists'].median(),
                'rebounds_median': self.data['Rebounds'].median(),
                'rebounds_75th': self.data['Rebounds'].quantile(0.75),
                'blocks_median': self.data['Blocks'].median()
            }
        }

        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"✅ Model saved as '{filename}'")

    def load_model(self, filename='nba_predictor_model.pkl'):
        """Load a previously trained model."""
        import pickle

        with open(filename, 'rb') as f:
            model_data = pickle.load(f)

        self.best_model = model_data['best_model']
        self.label_encoders = model_data['label_encoders']
        self.scaler = model_data['scaler']
        self.feature_names = model_data['feature_names']

        print(f"✅ Model loaded from '{filename}'")

    def run_complete_pipeline(self):
        """Run the complete machine learning pipeline."""
        print("🚀 Starting NBA Performance Prediction Pipeline...")

        # Step 1: Load and explore data
        self.load_and_explore_data()

        # Step 2: Create visualizations
        self.visualize_data()

        # Step 3: Preprocess data
        self.preprocess_data()

        # Step 4: Train models
        self.train_models()

        # Step 5: Hyperparameter tuning
        self.hyperparameter_tuning()

        # Step 6: Evaluate model
        self.evaluate_model()

        # Step 7: Save model
        self.save_model()

        print("\n🎉 Pipeline completed successfully!")
        print(f"Best model: {self.best_model['name']} with R² = {self.best_model['test_r2']:.4f}")

        return self


def main():
    """Main function to demonstrate the NBA Performance Predictor."""
    # Initialize predictor
    predictor = NBAPerformancePredictor()

    # Run complete pipeline
    predictor.run_complete_pipeline()

    # Example prediction on new data
    print("\n" + "="*60)
    print("EXAMPLE PREDICTION")
    print("="*60)

    # Create sample data for prediction
    sample_data = {
        'Player': ['LeBron James'],
        'Opponent': ['LAL'],
        'Minutes_Played': [35],
        'Last5_AvgPoints': [28.5],
        'Home_Game': [1],
        'Rebounds': [8],
        'Assists': [7],
        'Steals': [1.2],
        'Blocks': [0.8]
    }

    # Make prediction
    prediction_result = predictor.predict_new_data(sample_data)
    print("\nSample Prediction:")
    print(prediction_result)

    # Show how to use the model for different scenarios
    print("\n📋 Usage Instructions:")
    print("1. Load the saved model: predictor.load_model('nba_predictor_model.pkl')")
    print("2. Prepare new data with required features")
    print("3. Call predictor.predict_new_data(new_data)")
    print("4. Features needed:", predictor.feature_names)


if __name__ == "__main__":
    main()
