# NBA Player Performance Predictor

A comprehensive machine learning system to predict NBA player points scored based on game statistics and historical performance data.

## 🏀 Overview

This project implements a complete machine learning pipeline that:
- Analyzes NBA player performance data
- Predicts points scored based on various game features
- Compares multiple ML algorithms
- Provides detailed performance evaluation
- Includes data visualization and feature importance analysis

## 📊 Dataset

The model uses `nba_player_full_stats.csv` with the following features:

### Input Features:
- **Player**: Player name
- **Opponent**: Opposing team
- **Minutes_Played**: Minutes played in the game
- **Last5_AvgPoints**: Average points in last 5 games
- **Home_Game**: Whether it's a home game (0/1)
- **Rebounds**: Rebounds in the game
- **Assists**: Assists in the game
- **Steals**: Steals in the game
- **Blocks**: Blocks in the game

### Target Variable:
- **Points_Scored**: Points scored in the game (what we predict)

## 🚀 Quick Start

### Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

### Running the Model

1. **Complete Pipeline** (recommended for first run):
```python
from nba_performance_predictor import NBAPerformancePredictor

# Initialize and run complete pipeline
predictor = NBAPerformancePredictor()
predictor.run_complete_pipeline()
```

2. **Step-by-step execution**:
```python
predictor = NBAPerformancePredictor()

# Load and explore data
predictor.load_and_explore_data()

# Create visualizations
predictor.visualize_data()

# Preprocess data
predictor.preprocess_data()

# Train models
predictor.train_models()

# Tune hyperparameters
predictor.hyperparameter_tuning()

# Evaluate model
predictor.evaluate_model()
```

3. **Making Predictions**:
```python
# Sample data for prediction
new_data = {
    'Player': ['LeBron James'],
    'Opponent': ['LAL'],
    'Minutes_Played': [35],
    'Last5_AvgPoints': [28.5],
    'Home_Game': [1],
    'Rebounds': [8],
    'Assists': [7],
    'Steals': [1.2],
    'Blocks': [0.8]
}

# Make prediction
results = predictor.predict_new_data(new_data)
print(results)
```

## 🤖 Machine Learning Models

The system trains and compares multiple algorithms:

1. **Linear Regression**: Simple baseline model
2. **Ridge Regression**: Linear model with L2 regularization
3. **Lasso Regression**: Linear model with L1 regularization
4. **Random Forest**: Ensemble of decision trees
5. **Gradient Boosting**: Sequential ensemble method

The best performing model is automatically selected based on test R² score.

## 📈 Features Engineering

The model creates additional features to improve predictions:

- **Total_Stats**: Sum of rebounds, assists, steals, and blocks
- **Offensive_Rating**: Points and assists per minute
- **Defensive_Rating**: Steals and blocks per minute
- **Points_vs_Recent**: Difference from recent average
- **Likely_Guard**: Estimated guard position based on stats
- **Likely_Center**: Estimated center position based on stats

## 📊 Model Evaluation

The system provides comprehensive evaluation including:

- **R² Score**: Coefficient of determination
- **RMSE**: Root Mean Square Error
- **MAE**: Mean Absolute Error
- **Cross-validation**: 5-fold cross-validation scores
- **Residual Analysis**: Distribution and patterns
- **Feature Importance**: For tree-based models
- **Performance by Range**: Accuracy across different point ranges

## 📁 Output Files

Running the pipeline generates:

1. **nba_data_analysis.png**: Comprehensive data exploration plots
2. **model_evaluation.png**: Model performance visualizations
3. **nba_predictor_model.pkl**: Saved trained model for future use

## 🔧 Advanced Usage

### Loading a Saved Model
```python
predictor = NBAPerformancePredictor()
predictor.load_model('nba_predictor_model.pkl')

# Now you can make predictions without retraining
results = predictor.predict_new_data(new_data)
```

### Custom Data Path
```python
predictor = NBAPerformancePredictor(data_path='path/to/your/data.csv')
```

## 📋 Requirements

- Python 3.7+
- pandas >= 1.5.0
- numpy >= 1.21.0
- scikit-learn >= 1.1.0
- matplotlib >= 3.5.0
- seaborn >= 0.11.0

## 🎯 Model Performance

The model typically achieves:
- **R² Score**: 0.75-0.85 (varies by dataset)
- **RMSE**: 3-5 points
- **MAE**: 2-4 points

Performance varies based on:
- Data quality and size
- Player consistency
- Feature relevance
- Model selection

## 🔍 Interpretation

### Feature Importance
The model identifies which factors most influence point prediction:
- Recent performance (Last5_AvgPoints)
- Minutes played
- Player identity
- Home/away status
- Supporting statistics (rebounds, assists)

### Prediction Confidence
- Higher confidence for consistent players
- Lower confidence for highly variable performers
- Better accuracy in typical scoring ranges (15-35 points)

## 🚨 Limitations

- Predictions based on historical patterns
- Cannot account for injuries or unusual circumstances
- Performance may vary for players with limited data
- Model assumes similar game conditions

## 📞 Support

For questions or issues:
1. Check the generated visualization files for insights
2. Review model evaluation metrics
3. Ensure data format matches expected structure
4. Verify all required features are present

## 🔄 Future Improvements

Potential enhancements:
- Include more advanced statistics (PER, usage rate)
- Add opponent defensive ratings
- Incorporate injury/rest status
- Time series analysis for trends
- Deep learning models for complex patterns
