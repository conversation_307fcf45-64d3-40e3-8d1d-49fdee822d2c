#!/usr/bin/env python3
"""
Example usage of the NBA Performance Predictor

This script demonstrates how to use the NBA Performance Predictor
for various scenarios including training, prediction, and model evaluation.
"""

from nba_performance_predictor import NBAPerformancePredictor
import pandas as pd

def example_complete_pipeline():
    """Example: Run the complete ML pipeline"""
    print("="*60)
    print("EXAMPLE 1: COMPLETE PIPELINE")
    print("="*60)
    
    # Initialize predictor
    predictor = NBAPerformancePredictor()
    
    # Run complete pipeline
    predictor.run_complete_pipeline()
    
    return predictor

def example_step_by_step():
    """Example: Step-by-step execution with custom analysis"""
    print("\n" + "="*60)
    print("EXAMPLE 2: STEP-BY-STEP EXECUTION")
    print("="*60)
    
    predictor = NBAPerformancePredictor()
    
    # Step 1: Load and explore
    data = predictor.load_and_explore_data()
    print(f"\nDataset shape: {data.shape}")
    print(f"Average points scored: {data['Points_Scored'].mean():.2f}")
    print(f"Points range: {data['Points_Scored'].min()}-{data['Points_Scored'].max()}")
    
    # Step 2: Visualizations
    predictor.visualize_data()
    
    # Step 3: Preprocessing
    X_train, X_test, y_train, y_test = predictor.preprocess_data()
    
    # Step 4: Train models
    model_scores = predictor.train_models()
    
    # Print model comparison
    print("\nModel Performance Comparison:")
    for name, scores in model_scores.items():
        print(f"{name:20} | Test R²: {scores['test_r2']:.4f} | RMSE: {scores['test_rmse']:.4f}")
    
    # Step 5: Hyperparameter tuning
    predictor.hyperparameter_tuning()
    
    # Step 6: Evaluation
    predictor.evaluate_model()
    
    return predictor

def example_predictions():
    """Example: Making predictions on new data"""
    print("\n" + "="*60)
    print("EXAMPLE 3: MAKING PREDICTIONS")
    print("="*60)
    
    # For this example, we'll use a pre-trained model
    # In practice, you would load a saved model or train one first
    predictor = NBAPerformancePredictor()
    
    # Quick training for demonstration
    predictor.load_and_explore_data()
    predictor.preprocess_data()
    predictor.train_models()
    
    # Example 1: Single player prediction
    print("\n🏀 Single Player Prediction:")
    single_player = {
        'Player': ['Stephen Curry'],
        'Opponent': ['LAL'],
        'Minutes_Played': [36],
        'Last5_AvgPoints': [29.2],
        'Home_Game': [1],  # Home game
        'Rebounds': [5],
        'Assists': [8],
        'Steals': [1.5],
        'Blocks': [0.3]
    }
    
    result = predictor.predict_new_data(single_player)
    print(result)
    
    # Example 2: Multiple players prediction
    print("\n🏀 Multiple Players Prediction:")
    multiple_players = {
        'Player': ['LeBron James', 'Kevin Durant', 'Giannis Antetokounmpo'],
        'Opponent': ['BOS', 'PHX', 'MIA'],
        'Minutes_Played': [35, 38, 34],
        'Last5_AvgPoints': [28.5, 31.2, 32.1],
        'Home_Game': [0, 1, 1],  # Away, Home, Home
        'Rebounds': [8, 6, 12],
        'Assists': [7, 5, 6],
        'Steals': [1.2, 0.8, 1.0],
        'Blocks': [0.8, 1.5, 1.2]
    }
    
    results = predictor.predict_new_data(multiple_players)
    print(results)
    
    return predictor

def example_model_analysis():
    """Example: Detailed model analysis"""
    print("\n" + "="*60)
    print("EXAMPLE 4: MODEL ANALYSIS")
    print("="*60)
    
    predictor = NBAPerformancePredictor()
    
    # Train model
    predictor.load_and_explore_data()
    predictor.preprocess_data()
    predictor.train_models()
    
    # Analyze best model
    best_model = predictor.best_model
    print(f"\nBest Model: {best_model['name']}")
    print(f"Test R² Score: {best_model['test_r2']:.4f}")
    print(f"Test RMSE: {best_model['test_rmse']:.4f}")
    
    # Feature importance (if available)
    if hasattr(best_model['model'], 'feature_importances_'):
        print("\nTop 5 Most Important Features:")
        feature_importance = pd.DataFrame({
            'feature': predictor.feature_names,
            'importance': best_model['model'].feature_importances_
        }).sort_values('importance', ascending=False)
        
        for i, (_, row) in enumerate(feature_importance.head().iterrows()):
            print(f"{i+1}. {row['feature']}: {row['importance']:.4f}")
    
    # Performance by player type
    print("\nAnalyzing predictions by player characteristics...")
    
    # Create test predictions
    X_test_use = predictor.X_test_scaled if best_model['use_scaled'] else predictor.X_test
    test_predictions = best_model['model'].predict(X_test_use)
    
    # Analyze accuracy
    errors = abs(predictor.y_test - test_predictions)
    print(f"Mean Absolute Error: {errors.mean():.2f} points")
    print(f"Median Absolute Error: {errors.median():.2f} points")
    print(f"90th Percentile Error: {errors.quantile(0.9):.2f} points")
    
    return predictor

def example_save_load_model():
    """Example: Saving and loading models"""
    print("\n" + "="*60)
    print("EXAMPLE 5: SAVE/LOAD MODEL")
    print("="*60)
    
    # Train and save model
    print("Training and saving model...")
    predictor1 = NBAPerformancePredictor()
    predictor1.load_and_explore_data()
    predictor1.preprocess_data()
    predictor1.train_models()
    predictor1.save_model('example_model.pkl')
    
    # Load model in new instance
    print("\nLoading model in new predictor instance...")
    predictor2 = NBAPerformancePredictor()
    predictor2.load_model('example_model.pkl')
    
    # Make prediction with loaded model
    test_data = {
        'Player': ['Test Player'],
        'Opponent': ['TEST'],
        'Minutes_Played': [30],
        'Last5_AvgPoints': [25.0],
        'Home_Game': [1],
        'Rebounds': [6],
        'Assists': [5],
        'Steals': [1.0],
        'Blocks': [0.5]
    }
    
    # Note: This will work once the model is properly trained with the data
    print("Model loaded successfully! Ready for predictions.")
    
    return predictor2

def main():
    """Run all examples"""
    print("🏀 NBA PERFORMANCE PREDICTOR - EXAMPLES")
    print("This script demonstrates various ways to use the predictor")
    
    try:
        # Example 1: Complete pipeline
        predictor = example_complete_pipeline()
        
        # Example 2: Step by step (commented out to avoid redundancy)
        # example_step_by_step()
        
        # Example 3: Predictions
        example_predictions()
        
        # Example 4: Model analysis
        example_model_analysis()
        
        # Example 5: Save/load (commented out to avoid file conflicts)
        # example_save_load_model()
        
        print("\n🎉 All examples completed successfully!")
        print("\nGenerated files:")
        print("- nba_data_analysis.png (data exploration plots)")
        print("- model_evaluation.png (model performance plots)")
        print("- nba_predictor_model.pkl (saved model)")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        print("Make sure 'nba_player_full_stats.csv' is in the current directory")

if __name__ == "__main__":
    main()
